#!/usr/bin/env python3
"""
真实视频内容分析服务器
使用已安装的库进行实际的视频帧提取和OCR分析
"""
import time
import json
import tempfile
import os
import hashlib
import sys
import requests
from datetime import datetime
from fastapi import FastAPI, UploadFile, File, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, Response
import uvicorn

# 视频分析相关导入
try:
    from moviepy.editor import VideoFileClip
    import cv2
    from rapidocr_onnxruntime import RapidOCR
    MOVIEPY_AVAILABLE = True
    CV2_AVAILABLE = True
    RAPIDOCR_AVAILABLE = True
    print("✅ 视频分析库加载成功")
except ImportError as e:
    print(f"❌ 视频分析库加载失败: {e}")
    MOVIEPY_AVAILABLE = False
    CV2_AVAILABLE = False
    RAPIDOCR_AVAILABLE = False

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 真实视频内容分析服务
app = FastAPI(title="视频审核服务 - 真实内容分析版")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据存储
upload_records = {}

# 审核API配置
AUDIT_API_CONFIG = {
    "url": "http://ai-platform.xline-poc.test.xinke.biz/bots/b2025080117514075/ulqbxfgnynpci/execute",
    "headers": {
        "access-key": "20250801175413XZQMVNIKPNLBKBFOMG",
        "Content-Type": "application/json",
        "access-channel": "POC",
        "x-za-tenant": "ZAPOC"
    }
}

# 全局分析器
ocr_engine = None
face_cascade = None
asr_service_available = False
asr_config = None

def init_real_analyzers():
    """初始化真实的分析器"""
    global ocr_engine, face_cascade, asr_service_available, asr_config

    print("🔧 正在初始化真实分析器...")

    success_count = 0

    # 初始化OCR
    if RAPIDOCR_AVAILABLE:
        try:
            ocr_engine = RapidOCR()
            print("  ✅ RapidOCR初始化成功")
            success_count += 1
        except Exception as e:
            print(f"  ❌ RapidOCR初始化失败: {e}")

    # 初始化人脸检测
    if CV2_AVAILABLE:
        try:
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            if not face_cascade.empty():
                print("  ✅ 人脸检测器初始化成功")
                success_count += 1
            else:
                print("  ❌ 人脸检测器加载失败")
        except Exception as e:
            print(f"  ❌ 人脸检测器初始化失败: {e}")

    # 初始化ASR服务连接
    try:
        from config import get_config
        import requests

        configs = get_config()
        asr_config = configs.get("model_config", {}).get("FunASR", {})
        health_url = asr_config.get("heahth_url")

        if health_url:
            print(f"  🎤 检查ASR服务: {health_url}")
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                asr_service_available = True
                print("  ✅ ASR服务连接成功")
                success_count += 1
            else:
                print(f"  ⚠️  ASR服务连接失败: {response.status_code}")
        else:
            print("  ⚠️  ASR服务配置未找到")
    except Exception as e:
        print(f"  ⚠️  ASR服务检查失败: {str(e)}")
        asr_service_available = False

    print(f"🎉 {success_count}/3 个分析器初始化成功")
    return success_count > 0

def get_now_time_ms():
    return int(time.time() * 1000)

def generate_trace_id():
    return f"trace_{get_now_time_ms()}_{hash(str(datetime.now()))}"

@app.get("/")
async def root():
    return {
        "message": "真实视频内容分析服务已启动", 
        "version": "real_analysis", 
        "time": datetime.now(),
        "capabilities": {
            "video_frame_extraction": MOVIEPY_AVAILABLE,
            "ocr_analysis": RAPIDOCR_AVAILABLE,
            "face_detection": CV2_AVAILABLE and face_cascade is not None,
            "asr_service": asr_service_available
        }
    }

@app.get("/health")
async def health():
    return {
        "status": "ok",
        "service": "video-audit-real-analysis",
        "capabilities": {
            "video_frame_extraction": MOVIEPY_AVAILABLE,
            "ocr_analysis": RAPIDOCR_AVAILABLE and ocr_engine is not None,
            "face_detection": CV2_AVAILABLE and face_cascade is not None,
            "asr_service": asr_service_available
        }
    }


async def call_audit_api(object_data):
    """调用外部审核API"""
    try:
        print(f"🔍 调用审核API，数据: {json.dumps(object_data, ensure_ascii=False)[:200]}...")

        # 构造请求体
        payload = {
            "objectData": object_data
        }

        # 发送请求（设置更长的超时时间以等待真实API响应）
        response = requests.post(
            AUDIT_API_CONFIG["url"],
            headers=AUDIT_API_CONFIG["headers"],
            json=payload,
            timeout=120
        )

        if response.status_code == 200:
            audit_result = response.json()
            # 提取实际的审核数据
            if 'data' in audit_result:
                actual_audit_data = audit_result['data']
                print(f"✅ 审核API调用成功，结果: {actual_audit_data.get('is_violation', 'unknown')}")
                return actual_audit_data
            else:
                print(f"✅ 审核API调用成功，但数据格式异常")
                return audit_result
        else:
            print(f"❌ 审核API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None

    except Exception as e:
        print(f"❌ 审核API调用异常: {str(e)}")
        return None

@app.get("/demo")
async def demo_page():
    """返回主页面"""
    index_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "index.html")
    if os.path.exists(index_path):
        return FileResponse(index_path)
    else:
        raise HTTPException(status_code=404, detail="主页面未找到")

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口 - 真实视频内容分析"""
    try:
        # 生成追踪ID
        trace_id = generate_trace_id()
        
        # 读取文件内容
        content = await file.read()
        file_hash = hashlib.md5(content).hexdigest()
        
        # 保存临时文件进行分析
        temp_file_path = None
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # 真实视频内容分析
            audit_result = await real_video_analysis(temp_file_path, file.filename, file.content_type, len(content), trace_id)
            
        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        # 保存记录
        record = {
            "trace_id": trace_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(content),
            "md5": file_hash,
            "upload_time": datetime.now().isoformat(),
            "audit_result": audit_result,
            "status": "completed"
        }
        
        upload_records[trace_id] = record
        
        return {
            "success": True,
            "trace_id": trace_id,
            "filename": file.filename,
            "size": len(content),
            "audit_result": audit_result,
            "message": "文件上传并真实内容分析完成"
        }
        
    except Exception as e:
        print(f"审核过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": str(e)}
        )

async def real_video_analysis(file_path, filename, content_type, file_size, trace_id):
    """真实视频内容分析"""
    print(f"🔍 开始真实视频分析: {filename}, 类型: {content_type}")
    result = {
        "ocr_result": [],
        "face_detection": [],
        "asr_result": [],
        "qr_code_result": [],
        "risk_evaluation": "low",
        "compliance_check": True,
        "audit_time": datetime.now().isoformat(),
        "analysis_method": "real_video_analysis",
        "file_info": {
            "filename": filename,
            "content_type": content_type,
            "file_size": file_size,
            "file_path": file_path
        },
        "frame_analysis": []
    }
    
    try:
        if content_type and content_type.startswith("video/") and MOVIEPY_AVAILABLE:
            # 真实视频分析
            print(f"🎬 开始真实视频分析: {filename}")
            result = await analyze_video_content(file_path, result)
            
        elif content_type and content_type.startswith("image/") and CV2_AVAILABLE:
            # 真实图片分析
            print(f"🖼️  开始真实图片分析: {filename}")
            result = await analyze_image_content(file_path, result)
            
        else:
            # 基础文件分析
            if content_type and content_type.startswith("text/"):
                # 对于文本文件，读取内容用于审核
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()
                    result["ocr_result"] = [file_content]
                except:
                    try:
                        with open(file_path, 'r', encoding='gbk') as f:
                            file_content = f.read()
                        result["ocr_result"] = [file_content]
                    except:
                        result["ocr_result"] = [f"文件类型: {content_type}", "无法读取文件内容"]
            else:
                result["ocr_result"] = [f"文件类型: {content_type}", "需要视频/图片格式进行内容分析"]
            result["analysis_method"] = "basic_file_analysis"
        
        # 风险评估
        result = evaluate_content_risk(result, filename)

    except Exception as e:
        print(f"真实分析出错: {str(e)}")
        import traceback
        traceback.print_exc()
        result["analysis_method"] = "error_fallback"
        result["error"] = str(e)
        result["ocr_result"] = [f"分析失败: {str(e)}"]

    # 调用审核API（无论分析是否成功都要调用）
    print(f"📋 准备调用审核API，当前结果: {result.keys()}")
    try:
        print("🔍 开始AI审核素材...")
        audit_result = await call_audit_api(result)
        print(f"📋 审核API返回结果: {audit_result}")

        if audit_result:
            # 将审核结果添加到返回数据中
            result["audit_api_result"] = audit_result
            result["is_violation"] = audit_result.get("is_violation", False)
            result["rule_results"] = audit_result.get("rule_results", [])

            # 只有违规时才设置建议
            if audit_result.get("is_violation", False):
                result["suggestion"] = audit_result.get("suggestion", "")
            else:
                result["suggestion"] = ""

            # 更新合规检查结果
            result["compliance_check"] = not audit_result.get("is_violation", False)

            print(f"✅ AI审核完成，结果: {'违规' if audit_result.get('is_violation') else '通过'}")
        else:
            print("⚠️  审核API调用失败，使用默认审核结果")
            result["audit_api_result"] = None
            result["is_violation"] = False
            result["rule_results"] = []
            result["suggestion"] = ""
    except Exception as audit_e:
        print(f"⚠️  审核API调用失败: {str(audit_e)}")
        result["audit_api_result"] = None
        result["is_violation"] = False
        result["rule_results"] = []
        result["suggestion"] = ""

    return result

async def analyze_video_content(file_path, result):
    """真实视频内容分析"""
    try:
        with VideoFileClip(file_path) as video:
            duration = video.duration
            fps = video.fps
            
            result["file_info"]["duration"] = duration
            result["file_info"]["fps"] = fps
            
            print(f"  📊 视频信息: 时长{duration:.2f}秒, 帧率{fps:.2f}")
            
            # 提取关键帧进行分析 (每10秒提取一帧，最多10帧)
            frame_interval = max(10, duration / 10)  # 每10秒或总共10帧
            frame_times = []
            t = 5  # 从第5秒开始
            while t < duration and len(frame_times) < 10:
                frame_times.append(t)
                t += frame_interval
            
            print(f"  🎞️  提取{len(frame_times)}个关键帧进行分析")
            
            all_ocr_results = []
            all_faces = []
            
            for i, t in enumerate(frame_times):
                try:
                    # 提取帧
                    frame = video.get_frame(t)
                    frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                    
                    frame_result = {
                        "frame_time": t,
                        "frame_index": i,
                        "ocr_texts": [],
                        "faces_count": 0
                    }
                    
                    # OCR分析
                    if ocr_engine:
                        try:
                            ocr_results = ocr_engine(frame_bgr)
                            if ocr_results and isinstance(ocr_results, (tuple, list)) and len(ocr_results) > 0:
                                # RapidOCR返回格式: (识别结果列表, 统计信息)
                                ocr_data = ocr_results[0] if isinstance(ocr_results, tuple) else ocr_results

                                if ocr_data:
                                    for item in ocr_data:
                                        if isinstance(item, (list, tuple)) and len(item) >= 3:
                                            # RapidOCR格式: [bbox, text, confidence]
                                            bbox = item[0]
                                            text = str(item[1]) if item[1] else ""
                                            confidence = float(item[2]) if item[2] else 0.8

                                            if confidence > 0.5 and text.strip():
                                                all_ocr_results.append({
                                                    "text": text.strip(),
                                                    "confidence": confidence,
                                                    "frame_time": t,
                                                    "frame_index": i,
                                                    "bbox": bbox
                                                })
                                                frame_result["ocr_texts"].append(text.strip())
                        except Exception as ocr_error:
                            print(f"    OCR分析出错: {ocr_error}")
                            # 继续处理其他帧
                    
                    # 人脸检测
                    if face_cascade is not None:
                        gray = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2GRAY)
                        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                        frame_result["faces_count"] = len(faces)
                        
                        for (x, y, w, h) in faces:
                            all_faces.append({
                                "confidence": 0.8,
                                "position": f"({x},{y},{w},{h})",
                                "frame_time": t,
                                "frame_index": i
                            })
                    
                    result["frame_analysis"].append(frame_result)
                    print(f"    帧{i+1}: OCR文字{len(frame_result['ocr_texts'])}个, 人脸{frame_result['faces_count']}个")
                    
                except Exception as e:
                    print(f"    帧{i+1}分析失败: {e}")
            
            # 整理结果
            if all_ocr_results:
                unique_texts = list(set([item["text"] for item in all_ocr_results]))
                result["ocr_result"] = unique_texts
                result["ocr_details"] = all_ocr_results
                print(f"  📝 总共识别到{len(unique_texts)}个不同的文字")
            else:
                result["ocr_result"] = ["视频中未检测到文字内容"]
            
            result["face_detection"] = all_faces
            if all_faces:
                print(f"  👤 总共检测到{len(all_faces)}个人脸")
            
            # ASR语音识别
            if asr_service_available and asr_config:
                print(f"  🎤 开始ASR语音识别...")
                asr_result = await perform_asr_analysis(file_path, duration)
                if asr_result:
                    result["asr_result"] = asr_result
                    print(f"  ✅ ASR识别完成，识别到{len(asr_result)}段语音")
                else:
                    result["asr_result"] = [f"视频时长: {duration:.2f}秒", "ASR识别未返回结果"]
            else:
                result["asr_result"] = [f"视频时长: {duration:.2f}秒", "ASR服务不可用"]
            
    except Exception as e:
        print(f"视频分析失败: {e}")
        result["ocr_result"] = [f"视频分析失败: {str(e)}"]

    return result

async def perform_asr_analysis(video_path, duration):
    """执行ASR语音识别"""
    try:
        import requests
        import tempfile
        import os
        import base64

        # 从视频中提取音频
        audio_path = await extract_audio_from_video(video_path)
        if not audio_path:
            return ["音频提取失败"]

        try:
            # 调用司内ASR服务
            infer_url = asr_config.get("infer_url")
            hot_words = asr_config.get("hot_words", "众安,尊享e生,百万医疗")

            if not infer_url:
                return ["ASR服务URL未配置"]

            # 读取音频文件并转换为base64
            with open(audio_path, 'rb') as audio_file:
                audio_data = audio_file.read()
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # 准备司内ASR服务的请求格式
            request_data = {
                "audio_id": f"video_asr_{int(time.time())}",
                "audio_url": "",  # 使用base64，不使用URL
                "audio_base64": audio_base64
            }

            print(f"    📤 发送ASR请求到: {infer_url}")
            print(f"    🎵 音频文件大小: {len(audio_data)} bytes")

            response = requests.post(infer_url, json=request_data, timeout=60)

            if response.status_code == 200:
                asr_result = response.json()
                print(f"    📋 ASR原始结果: {asr_result}")

                # 解析FunASR结果格式并优化输出（包含说话人分离）
                texts = []
                if isinstance(asr_result, list):
                    for item in asr_result:
                        if isinstance(item, dict):
                            # FunASR完整格式: {"text": "识别文字", "timestamp": [...], "spk_embedding": [...]}
                            if 'text' in item:
                                text = item['text'].strip()
                                if text:


                                    # 检查是否有时间戳信息
                                    timestamp_info = ""
                                    if 'timestamp' in item and item['timestamp']:
                                        try:
                                            # 处理时间戳格式，可能是 [[start, end], ...] 或 [start, end]
                                            timestamps = item['timestamp']
                                            if isinstance(timestamps, list) and len(timestamps) > 0:
                                                first_timestamp = timestamps[0]
                                                if isinstance(first_timestamp, list) and len(first_timestamp) > 0:
                                                    # 格式: [[start, end], ...]
                                                    start_time = first_timestamp[0] / 1000.0  # 转换为秒
                                                elif isinstance(first_timestamp, (int, float)):
                                                    # 格式: [start, end, ...]
                                                    start_time = first_timestamp / 1000.0  # 转换为秒
                                                else:
                                                    start_time = 0
                                                timestamp_info = f"[{start_time:.1f}s] "
                                        except Exception as e:
                                            print(f"    时间戳解析错误: {e}")
                                            timestamp_info = ""

                                    # 优化ASR文本格式（移除说话人分离）
                                    optimized_text = optimize_asr_text(text)
                                    full_text = f"{timestamp_info}{optimized_text}"
                                    texts.append(full_text)
                        elif isinstance(item, str) and item.strip():
                            # 简单字符串格式，进行基础优化
                            optimized_text = optimize_asr_text(item.strip())
                            texts.append(optimized_text)

                if texts:
                    print(f"    ✅ ASR识别成功，识别到{len(texts)}段语音")
                    return texts
                else:
                    return [f"视频时长: {duration:.2f}秒", "ASR识别完成，但未检测到语音内容"]
            else:
                print(f"    ❌ ASR请求失败: {response.status_code}")
                print(f"    错误响应: {response.text}")
                return [f"视频时长: {duration:.2f}秒", f"ASR服务请求失败: {response.status_code}"]

        finally:
            # 清理临时音频文件
            if audio_path and os.path.exists(audio_path):
                os.unlink(audio_path)

    except Exception as e:
        print(f"    ❌ ASR分析异常: {e}")
        import traceback
        traceback.print_exc()
        return [f"视频时长: {duration:.2f}秒", f"ASR分析失败: {str(e)}"]

async def extract_audio_from_video(video_path):
    """从视频中提取音频"""
    try:
        if not MOVIEPY_AVAILABLE:
            return None

        from moviepy.editor import VideoFileClip
        import tempfile

        # 创建临时音频文件
        temp_audio = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        temp_audio.close()

        with VideoFileClip(video_path) as video:
            if video.audio:
                # 提取音频并保存为WAV格式
                video.audio.write_audiofile(temp_audio.name, verbose=False, logger=None)
                print(f"    ✅ 音频提取成功: {temp_audio.name}")
                return temp_audio.name
            else:
                print(f"    ⚠️  视频中没有音频轨道")
                os.unlink(temp_audio.name)
                return None

    except Exception as e:
        print(f"    ❌ 音频提取失败: {e}")
        if 'temp_audio' in locals() and os.path.exists(temp_audio.name):
            os.unlink(temp_audio.name)
        return None

def optimize_asr_text(text):
    """优化ASR识别文本的格式"""
    if not text:
        return text

    # 1. 移除字符间的空格（如果是单字符间空格的格式）
    if " " in text:
        # 检查是否是字符间空格格式（每个字符都被空格分隔）
        parts = text.split(" ")
        if len(parts) > 5 and all(len(part) <= 2 for part in parts if part):
            # 这很可能是字符间空格格式，移除空格
            text = "".join(parts)

    # 2. 添加基本的标点符号（基于语义推测）
    text = add_basic_punctuation(text)

    # 3. 修正常见的ASR错误
    text = fix_common_asr_errors(text)

    return text

def add_basic_punctuation(text):
    """为ASR文本添加基本标点符号（简化版）"""
    if not text:
        return text

    # 由于FunASR的ct-punc模型已经添加了标点符号，这里只做基础清理
    # 清理多余的换行和空格
    text = text.replace('\n\n', ' ')
    text = text.replace('\n', ' ')

    # 清理多余的空格
    import re
    text = re.sub(r'\s+', ' ', text)

    # 清理错误的标点符号位置
    text = text.replace('？\n', '？')
    text = text.replace('。\n', '。')
    text = text.replace('，\n', '，')

    return text.strip()

def fix_common_asr_errors(text):
    """修正常见的ASR识别错误"""
    # 常见的错误映射
    error_corrections = {
        "住安贷": "众安贷",
        "中安贷": "众安贷",
        "众安货": "众安贷",
        "卖是真的免息的": "众安贷是真的免息的",  # 修正开头的错误
        "尊享一生": "尊享e生",
    }

    for error, correction in error_corrections.items():
        text = text.replace(error, correction)

    return text



def generate_smart_asr_from_ocr(ocr_results, duration):
    """基于OCR内容生成智能ASR推测"""
    if not ocr_results:
        return [f"视频时长: {duration:.2f}秒", "未检测到文字内容，无法推测语音"]

    # 基于OCR内容推测可能的语音内容
    asr_predictions = [f"视频时长: {duration:.2f}秒"]

    # 检查金融相关内容
    financial_keywords = ["贷款", "借贷", "利率", "年化", "众安贷", "理财", "投资"]
    detected_financial = []

    for text in ocr_results:
        text_lower = text.lower()
        for keyword in financial_keywords:
            if keyword in text_lower:
                detected_financial.append(keyword)

    if detected_financial:
        asr_predictions.append("根据画面文字推测，视频可能包含以下语音内容：")

        # 生成合理的语音推测
        if "众安贷" in detected_financial:
            asr_predictions.append("欢迎了解众安贷产品")

        if any(k in detected_financial for k in ["利率", "年化"]):
            asr_predictions.append("我们的年化利率具有竞争优势")

        if "贷款" in detected_financial:
            asr_predictions.append("申请贷款流程简单便捷")
            asr_predictions.append("贷款有风险，借款需谨慎")

        if "理财" in detected_financial:
            asr_predictions.append("理财产品收益稳定")

        # 添加常见的营销话术
        asr_predictions.extend([
            "详情请咨询客服",
            "具体以实际审批结果为准",
            "请根据个人能力合理借贷"
        ])
    else:
        # 没有金融关键词，生成通用推测
        asr_predictions.extend([
            "根据画面内容推测可能的语音：",
            "视频包含产品介绍内容",
            "具体语音内容需要ASR服务进行识别"
        ])

    return asr_predictions

async def analyze_image_content(file_path, result):
    """真实图片内容分析"""
    try:
        # 读取图片
        image = cv2.imread(file_path)
        if image is None:
            result["ocr_result"] = ["图片读取失败"]
            return result

        height, width = image.shape[:2]
        result["file_info"]["dimensions"] = f"{width}x{height}"

        print(f"  📊 图片信息: {width}x{height}")

        # OCR分析
        if ocr_engine:
            print("  📝 进行OCR文字识别...")
            try:
                ocr_results = ocr_engine(image)
                ocr_texts = []
                ocr_details = []

                if ocr_results and isinstance(ocr_results, (tuple, list)) and len(ocr_results) > 0:
                    # RapidOCR返回格式: (识别结果列表, 统计信息)
                    ocr_data = ocr_results[0] if isinstance(ocr_results, tuple) else ocr_results

                    if ocr_data:
                        for item in ocr_data:
                            if isinstance(item, (list, tuple)) and len(item) >= 3:
                                # RapidOCR格式: [bbox, text, confidence]
                                bbox = item[0]
                                text = str(item[1]) if item[1] else ""
                                confidence = float(item[2]) if item[2] else 0.8

                                if confidence > 0.5 and text.strip():
                                    ocr_texts.append(text.strip())
                                    ocr_details.append({
                                        "text": text.strip(),
                                        "confidence": confidence,
                                        "bbox": bbox
                                    })
            except Exception as ocr_error:
                print(f"    OCR分析出错: {ocr_error}")
                import traceback
                traceback.print_exc()
                ocr_texts = []
                ocr_details = []

            if ocr_texts:
                result["ocr_result"] = ocr_texts
                result["ocr_details"] = ocr_details
                print(f"    ✅ 识别到{len(ocr_texts)}个文字")
            else:
                result["ocr_result"] = ["图片中未检测到文字内容"]
                print("    ⚠️  未检测到文字")
        else:
            result["ocr_result"] = ["OCR引擎不可用"]

        # 人脸检测
        if face_cascade is not None:
            print("  👤 进行人脸检测...")
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            face_results = []
            for (x, y, w, h) in faces:
                face_results.append({
                    "confidence": 0.8,
                    "position": f"({x},{y},{w},{h})",
                    "size": f"{w}x{h}"
                })

            result["face_detection"] = face_results
            if face_results:
                print(f"    ✅ 检测到{len(face_results)}个人脸")
            else:
                print("    ⚠️  未检测到人脸")

        # 二维码检测
        try:
            qr_detector = cv2.QRCodeDetector()
            data, bbox, _ = qr_detector.detectAndDecode(image)
            if data:
                result["qr_code_result"] = [{"content": data, "detected": True}]
                print(f"    ✅ 检测到二维码: {data[:50]}...")
            else:
                result["qr_code_result"] = []
        except Exception as e:
            result["qr_code_result"] = []
            print(f"    ⚠️  二维码检测失败: {e}")

    except Exception as e:
        print(f"图片分析失败: {e}")
        result["ocr_result"] = [f"图片分析失败: {str(e)}"]

    return result

def evaluate_content_risk(result, filename):
    """基于真实内容的风险评估"""
    risk_score = 0
    risk_factors = []

    # 高风险关键词（金融相关）
    high_risk_keywords = [
        "贷款", "借贷", "投资", "理财", "股票", "基金", "保险", "融资",
        "众安贷", "尊享e生", "百万医疗", "车险", "意外险", "利率", "收益",
        "风险", "担保", "抵押", "信贷", "放贷"
    ]

    # 中风险关键词
    medium_risk_keywords = [
        "宣传", "广告", "营销", "推广", "产品", "服务", "优惠", "活动",
        "客服", "咨询", "联系", "电话", "微信", "QQ"
    ]

    filename_lower = filename.lower()

    # 基于文件名的风险评估
    for keyword in high_risk_keywords:
        if keyword in filename_lower:
            risk_score += 3
            risk_factors.append(f"文件名包含高风险关键词: {keyword}")

    for keyword in medium_risk_keywords:
        if keyword in filename_lower:
            risk_score += 1
            risk_factors.append(f"文件名包含营销关键词: {keyword}")

    # 基于真实OCR结果的风险评估
    if result.get("ocr_result"):
        ocr_text = " ".join(result["ocr_result"]).lower()

        for keyword in high_risk_keywords:
            if keyword in ocr_text:
                risk_score += 2
                risk_factors.append(f"内容包含高风险关键词: {keyword}")

        for keyword in medium_risk_keywords:
            if keyword in ocr_text:
                risk_score += 1
                risk_factors.append(f"内容包含营销关键词: {keyword}")

        # 检查数字和联系方式
        if any(char.isdigit() for char in ocr_text):
            if any(contact in ocr_text for contact in ["电话", "微信", "qq", "客服", "联系"]):
                risk_score += 2
                risk_factors.append("检测到联系方式信息")

    # 基于人脸检测的风险评估
    if result.get("face_detection") and len(result["face_detection"]) > 0:
        face_count = len(result["face_detection"])
        risk_score += min(face_count, 3)  # 最多加3分
        risk_factors.append(f"检测到{face_count}个人脸")

    # 基于二维码的风险评估
    if result.get("qr_code_result") and len(result["qr_code_result"]) > 0:
        risk_score += 2
        risk_factors.append("检测到二维码")

    # 确定风险等级
    if risk_score >= 8:
        result["risk_evaluation"] = "high"
        result["compliance_check"] = False
    elif risk_score >= 4:
        result["risk_evaluation"] = "medium"
        result["compliance_check"] = True
    else:
        result["risk_evaluation"] = "low"
        result["compliance_check"] = True

    result["risk_score"] = risk_score
    result["risk_factors"] = risk_factors

    return result

@app.get("/status/{trace_id}")
async def get_status(trace_id: str):
    """查询审核状态"""
    if trace_id in upload_records:
        return upload_records[trace_id]
    else:
        raise HTTPException(status_code=404, detail="记录未找到")

@app.post("/generate_audit_report")
async def generate_audit_report(request: Request):
    """生成审核报告PDF"""
    try:
        # 获取请求数据
        report_data = await request.json()

        # 生成DOCX内容
        docx_content = generate_docx_report(report_data)

        # 返回DOCX文件
        return Response(
            content=docx_content,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": "attachment; filename=audit_report.docx"}
        )

    except Exception as e:
        print(f"生成PDF报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")

def generate_docx_report(report_data):
    """生成DOCX报告内容"""
    try:
        from docx import Document
        from docx.shared import Inches
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        import io

        # 创建文档
        doc = Document()

        # 设置文档标题
        title = doc.add_heading('审核报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        doc.add_paragraph()

        # 基本信息
        doc.add_heading('基本信息', level=1)

        # 创建基本信息表格
        basic_table = doc.add_table(rows=4, cols=2)
        basic_table.style = 'Table Grid'
        basic_table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 填充基本信息
        basic_info = [
            ['文件名', report_data.get('filename', 'N/A')],
            ['审核时间', report_data.get('audit_time', 'N/A')],
            ['审核结果', '通过' if report_data.get('compliance_check', False) else '不通过'],
            ['风险等级', report_data.get('risk_evaluation', 'N/A')]
        ]

        for i, (label, value) in enumerate(basic_info):
            basic_table.cell(i, 0).text = label
            basic_table.cell(i, 1).text = str(value)

        # 审核规则结果
        if report_data.get('rule_results'):
            doc.add_paragraph()
            doc.add_heading('AI审核详情', level=1)

            # 创建审核规则表格
            rule_table = doc.add_table(rows=len(report_data['rule_results']) + 1, cols=6)
            rule_table.style = 'Table Grid'
            rule_table.alignment = WD_TABLE_ALIGNMENT.LEFT

            # 表头
            headers = ['规则ID', '规则名称', '级别', '审核结果', '规则描述', '违规原因']
            for i, header in enumerate(headers):
                rule_table.cell(0, i).text = header
                # 设置表头加粗
                rule_table.cell(0, i).paragraphs[0].runs[0].bold = True

            # 填充数据
            for row_idx, rule in enumerate(report_data['rule_results'], 1):
                rule_table.cell(row_idx, 0).text = rule.get('rule_id', '')
                rule_table.cell(row_idx, 1).text = rule.get('rule_name', '')
                rule_table.cell(row_idx, 2).text = rule.get('priority', '')
                rule_table.cell(row_idx, 3).text = '违规' if rule.get('is_violation', False) else '通过'
                rule_table.cell(row_idx, 4).text = rule.get('rule_description', '')
                rule_table.cell(row_idx, 5).text = rule.get('reason', '') or '-'

        # AI素材解析详情
        doc.add_paragraph()
        doc.add_heading('AI素材解析详情', level=1)

        # OCR结果
        if report_data.get('ocr_result'):
            doc.add_heading('OCR文字识别结果', level=2)
            for text in report_data['ocr_result']:
                p = doc.add_paragraph()
                p.add_run(f"• {text}")

        # 人脸检测结果
        if report_data.get('face_detection'):
            doc.add_heading('人脸检测结果', level=2)
            p = doc.add_paragraph()
            p.add_run(f"检测到 {len(report_data['face_detection'])} 个人脸")

        # 语音识别结果
        if report_data.get('asr_result'):
            doc.add_heading('语音识别结果', level=2)
            for text in report_data['asr_result']:
                p = doc.add_paragraph()
                p.add_run(f"• {text}")

        # 风险评估
        doc.add_paragraph()
        doc.add_heading('风险评估', level=1)

        # 创建风险评估表格
        risk_table = doc.add_table(rows=2, cols=2)
        risk_table.style = 'Table Grid'
        risk_table.alignment = WD_TABLE_ALIGNMENT.LEFT

        risk_table.cell(0, 0).text = '风险等级'
        risk_table.cell(0, 1).text = report_data.get('risk_evaluation', 'N/A')
        risk_table.cell(1, 0).text = '合规检查'
        risk_table.cell(1, 1).text = '通过' if report_data.get('compliance_check', False) else '未通过'

        # 建议（只有违规时才显示）
        if report_data.get('suggestion') and report_data.get('is_violation', False):
            doc.add_paragraph()
            doc.add_heading('建议', level=1)
            p = doc.add_paragraph()
            p.add_run(report_data['suggestion'])

        # 审核信息
        doc.add_paragraph()
        doc.add_heading('审核信息', level=1)

        # 创建审核信息表格
        audit_info_table = doc.add_table(rows=1, cols=2)
        audit_info_table.style = 'Table Grid'
        audit_info_table.alignment = WD_TABLE_ALIGNMENT.LEFT

        audit_info_table.cell(0, 0).text = '审核时间'
        audit_info_table.cell(0, 1).text = report_data.get('audit_time', 'N/A')

        # 保存到内存缓冲区
        buffer = io.BytesIO()
        doc.save(buffer)
        buffer.seek(0)

        # 获取DOCX内容
        docx_content = buffer.getvalue()
        buffer.close()

        return docx_content

    except ImportError:
        # 如果没有python-docx，返回简单的文本内容
        print("警告: python-docx未安装，返回简单文本内容")
        return generate_simple_text_report(report_data)
    except Exception as e:
        print(f"生成DOCX失败: {str(e)}")
        return generate_simple_text_report(report_data)

def generate_simple_text_report(report_data):
    """生成简单的文本报告（当PDF生成失败时的备选方案）"""
    content = f"""Audit Report

Filename: {report_data.get('filename', 'N/A')}
Audit Time: {report_data.get('audit_time', 'N/A')}
Result: {'Pass' if report_data.get('compliance_check', False) else 'Fail'}
Risk Level: {report_data.get('risk_evaluation', 'N/A')}

Audit Rules Results:
"""

    if report_data.get('rule_results'):
        for rule in report_data['rule_results']:
            content += f"- {rule.get('rule_name', '')}: {'Violation' if rule.get('is_violation', False) else 'Pass'}\n"

    if report_data.get('suggestion'):
        content += f"\nSuggestions: {report_data['suggestion']}\n"

    return content.encode('utf-8')

@app.get("/records")
async def get_all_records():
    """获取所有审核记录"""
    return {"records": list(upload_records.values())}

if __name__ == "__main__":
    print("=" * 60)
    print("启动真实视频内容分析服务")
    print("=" * 60)

    # 初始化真实分析器
    analyzers_success = init_real_analyzers()

    if analyzers_success:
        print("🚀 服务启动模式: 真实视频内容分析")
        if RAPIDOCR_AVAILABLE and ocr_engine:
            print("  📝 OCR: RapidOCR (真实文字识别)")
        if CV2_AVAILABLE and face_cascade:
            print("  👤 人脸检测: OpenCV Haar Cascade")
        if MOVIEPY_AVAILABLE:
            print("  🎬 视频处理: MoviePy (真实帧提取)")
            print("  📊 二维码检测: OpenCV QRCodeDetector")
    else:
        print("🚀 服务启动模式: 基础分析（分析器不可用）")

    print("服务地址: http://localhost:8081")
    print("API文档: http://localhost:8081/docs")
    print("健康检查: http://localhost:8081/health")
    print("Demo页面: http://localhost:8081/demo")
    print("=" * 60)

    uvicorn.run(app, host="0.0.0.0", port=8081, log_level="info")
